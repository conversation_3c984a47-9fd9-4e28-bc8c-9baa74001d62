# 容量数据收集系统

## 系统概述

本系统用于每日自动收集容量监控数据，包括存储、数据库、容器、虚拟化四个方面的容量信息，并提供历史数据分析功能。

## 文件结构

```
├── capacity_tables_ddl.sql              # 数据库表结构定义
├── daily_capacity_collector.py          # 每日数据收集脚本
├── capacity_collector_config.py         # 配置文件
├── schedule_capacity_collector.py       # 定时任务管理脚本
├── capacity_data_analyzer.py            # 数据分析脚本
└── 容量数据收集系统说明.md              # 本说明文档
```

## 数据库表结构

### 1. 存储容量历史表 (storage_capacity_history)
- 记录存储池的容量信息
- 包含总容量、已用容量、使用率、健康状态等字段

### 2. 数据库容量历史表 (database_capacity_history)
- 记录数据库实例的容量信息
- 包含总容量、已用容量、使用率、健康状态等字段

### 3. 容器集群容量历史表 (container_capacity_history)
- 记录容器集群的资源使用情况
- 包含节点数、Pod数、CPU、内存、存储使用情况

### 4. 虚拟化集群容量历史表 (virtualization_capacity_history)
- 记录虚拟化集群的资源使用情况
- 包含主机数、虚拟机数、CPU、内存使用情况

### 5. 数据收集日志表 (capacity_collection_log)
- 记录每次数据收集的执行情况
- 包含执行状态、记录数量、错误信息等

## 安装和配置

### 1. 创建数据库表
```bash
# 在PostgreSQL中执行DDL脚本
psql -h ************* -p 5444 -U cloud -d cloud -f capacity_tables_ddl.sql
```

### 2. 安装Python依赖
```bash
pip install psycopg2-binary requests pandas matplotlib seaborn openpyxl schedule
```

### 3. 配置参数
编辑 `capacity_collector_config.py` 文件，修改以下配置：
- API_CONFIG: API服务地址和超时设置
- DATABASE_CONFIG: 数据库连接参数
- SCHEDULE_CONFIG: 定时任务执行时间

### 4. 环境变量配置（可选）
```bash
export CAPACITY_API_URL="http://127.0.0.1:5002"
export CAPACITY_DB_HOST="*************"
export CAPACITY_DB_PORT="5444"
export CAPACITY_DB_NAME="cloud"
export CAPACITY_DB_USER="cloud"
export CAPACITY_DB_PASSWORD="Cl123456"
```

## 使用方法

### 1. 手动执行数据收集
```bash
# 立即执行一次数据收集
python daily_capacity_collector.py
```

### 2. 设置定时任务

#### Windows系统
```bash
# 创建Windows定时任务（每日02:00执行）
python schedule_capacity_collector.py --mode install-windows
```

#### Linux系统
```bash
# 创建Linux cron任务（每日02:00执行）
python schedule_capacity_collector.py --mode install-linux
```

#### 使用内置调度器
```bash
# 启动内置定时调度器
python schedule_capacity_collector.py --mode schedule
```

### 3. 数据分析和报告
```bash
# 生成容量分析报告
python capacity_data_analyzer.py
```

## API接口依赖

系统依赖以下API接口（来自app.py）：
- `/api/storage` - 获取存储容量数据
- `/api/database` - 获取数据库容量数据
- `/api/container` - 获取容器集群容量数据
- `/api/virtualization` - 获取虚拟化集群容量数据

## 数据收集流程

1. **连接数据库** - 建立PostgreSQL连接
2. **调用API接口** - 依次获取四类容量数据
3. **数据处理** - 解析API响应，提取关键字段
4. **数据存储** - 插入到对应的历史表中
5. **日志记录** - 记录执行结果到日志表
6. **断开连接** - 清理资源

## 健康度判断标准

### 存储健康度
- 🟢 正常: 使用率 < 90%
- 🟡 观察: 使用率 90% ~ 95%
- 🔴 警告: 使用率 > 95%

### 数据库健康度
- 🟢 正常: 使用率 < 85%
- 🟡 观察: 使用率 85% ~ 95%
- 🔴 警告: 使用率 > 95%

### 容器健康度
- CPU/内存: 🟢 <80%, 🟡 80-90%, 🔴 >90%
- 存储: 🟢 <90%, 🟡 90-95%, 🔴 >95%

### 虚拟化健康度
- CPU/内存: 🟢 <75%, 🟡 75-85%, 🔴 >85%

## 数据分析功能

### 1. 历史数据查询
- 支持按时间范围查询各类型容量数据
- 提供DataFrame格式的结构化数据

### 2. 趋势分析
- 分析使用率变化趋势
- 计算平均增长率和预测

### 3. 报告生成
- 生成每日容量汇总报告
- 包含健康状态统计和趋势分析

### 4. 数据导出
- 支持导出Excel格式的历史数据
- 按资源类型分工作表组织

## 监控和告警

### 日志文件
- `capacity_collector.log` - 数据收集日志
- `schedule_capacity_collector.log` - 定时任务日志

### 数据库监控
通过 `capacity_collection_log` 表监控收集状态：
```sql
-- 查看最近的收集状态
SELECT * FROM capacity_collection_log 
ORDER BY created_at DESC LIMIT 10;

-- 查看失败的收集任务
SELECT * FROM capacity_collection_log 
WHERE status = 'FAILED' 
ORDER BY created_at DESC;
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库配置参数
   - 确认网络连通性
   - 验证用户权限

2. **API调用失败**
   - 检查API服务是否运行
   - 验证API地址配置
   - 查看API服务日志

3. **定时任务未执行**
   - 检查系统时间设置
   - 验证定时任务配置
   - 查看任务调度日志

### 调试模式
```bash
# 启用详细日志
export CAPACITY_LOG_LEVEL="DEBUG"
python daily_capacity_collector.py
```

## 维护建议

1. **定期清理历史数据** - 建议保留1年的历史数据
2. **监控磁盘空间** - 确保数据库有足够存储空间
3. **备份重要数据** - 定期备份容量历史数据
4. **更新依赖包** - 定期更新Python依赖包
5. **性能优化** - 根据数据量调整批处理大小

## 扩展功能

系统支持以下扩展：
- 添加新的资源类型监控
- 集成告警通知功能
- 开发Web管理界面
- 支持多数据中心部署
