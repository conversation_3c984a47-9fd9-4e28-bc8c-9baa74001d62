#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
容量数据分析脚本
提供历史数据查询、趋势分析、报告生成等功能
"""

import psycopg2
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Tuple
import logging
from capacity_collector_config import DATABASE_CONFIG

# 配置中文字体和日志
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CapacityAnalyzer:
    """容量数据分析器"""
    
    def __init__(self):
        self.db_conn = None
        
    def connect_db(self) -> bool:
        """连接数据库"""
        try:
            self.db_conn = psycopg2.connect(**DATABASE_CONFIG)
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def disconnect_db(self):
        """断开数据库连接"""
        if self.db_conn:
            self.db_conn.close()
    
    def query_storage_history(self, days: int = 30) -> pd.DataFrame:
        """查询存储容量历史数据"""
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
        
        query = """
        SELECT record_date, pool_name, pool_id, 
               total_capacity_gb, used_capacity_gb, usage_rate, health_status
        FROM storage_capacity_history 
        WHERE record_date BETWEEN %s AND %s
        ORDER BY record_date, pool_name
        """
        
        return pd.read_sql(query, self.db_conn, params=(start_date, end_date))
    
    def query_database_history(self, days: int = 30) -> pd.DataFrame:
        """查询数据库容量历史数据"""
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
        
        query = """
        SELECT record_date, db_name, db_id,
               total_capacity_gb, used_capacity_gb, usage_rate, health_status
        FROM database_capacity_history 
        WHERE record_date BETWEEN %s AND %s
        ORDER BY record_date, db_name
        """
        
        return pd.read_sql(query, self.db_conn, params=(start_date, end_date))
    
    def query_container_history(self, days: int = 30) -> pd.DataFrame:
        """查询容器集群容量历史数据"""
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
        
        query = """
        SELECT record_date, cluster_name, cluster_id, node_count, pod_count,
               cpu_total_cores, cpu_usage_rate, memory_total_gb, memory_usage_rate,
               storage_total_gb, storage_usage_rate, health_status
        FROM container_capacity_history 
        WHERE record_date BETWEEN %s AND %s
        ORDER BY record_date, cluster_name
        """
        
        return pd.read_sql(query, self.db_conn, params=(start_date, end_date))
    
    def query_virtualization_history(self, days: int = 30) -> pd.DataFrame:
        """查询虚拟化集群容量历史数据"""
        end_date = date.today()
        start_date = end_date - timedelta(days=days)
        
        query = """
        SELECT record_date, cluster_name, cluster_id, host_number, vm_number,
               cpu_total_cores, cpu_usage_rate, memory_total_gb, memory_usage_rate, health_status
        FROM virtualization_capacity_history 
        WHERE record_date BETWEEN %s AND %s
        ORDER BY record_date, cluster_name
        """
        
        return pd.read_sql(query, self.db_conn, params=(start_date, end_date))
    
    def get_daily_summary(self, target_date: date = None) -> Dict:
        """获取指定日期的容量汇总"""
        if target_date is None:
            target_date = date.today()
        
        query = """
        SELECT resource_type, total_count, normal_count, warning_count, critical_count, avg_usage_rate
        FROM daily_capacity_summary 
        WHERE record_date = %s
        ORDER BY resource_type
        """
        
        df = pd.read_sql(query, self.db_conn, params=(target_date,))
        return df.to_dict('records')
    
    def analyze_usage_trend(self, resource_type: str, days: int = 30) -> Dict:
        """分析使用率趋势"""
        if resource_type == 'storage':
            df = self.query_storage_history(days)
            usage_col = 'usage_rate'
        elif resource_type == 'database':
            df = self.query_database_history(days)
            usage_col = 'usage_rate'
        elif resource_type == 'container':
            df = self.query_container_history(days)
            # 计算平均使用率
            df['avg_usage_rate'] = (df['cpu_usage_rate'] + df['memory_usage_rate'] + df['storage_usage_rate']) / 3
            usage_col = 'avg_usage_rate'
        elif resource_type == 'virtualization':
            df = self.query_virtualization_history(days)
            # 计算平均使用率
            df['avg_usage_rate'] = (df['cpu_usage_rate'] + df['memory_usage_rate']) / 2
            usage_col = 'avg_usage_rate'
        else:
            raise ValueError(f"不支持的资源类型: {resource_type}")
        
        if df.empty:
            return {'trend': 'no_data', 'message': '无历史数据'}
        
        # 计算趋势
        daily_avg = df.groupby('record_date')[usage_col].mean().reset_index()
        daily_avg = daily_avg.sort_values('record_date')
        
        if len(daily_avg) < 2:
            return {'trend': 'insufficient_data', 'message': '数据不足以分析趋势'}
        
        # 计算趋势斜率
        x = range(len(daily_avg))
        y = daily_avg[usage_col].values
        slope = (y[-1] - y[0]) / (len(y) - 1)
        
        # 判断趋势
        if slope > 1:
            trend = 'increasing'
            message = f'使用率呈上升趋势，平均每日增长 {slope:.2f}%'
        elif slope < -1:
            trend = 'decreasing'
            message = f'使用率呈下降趋势，平均每日下降 {abs(slope):.2f}%'
        else:
            trend = 'stable'
            message = '使用率相对稳定'
        
        return {
            'trend': trend,
            'message': message,
            'slope': slope,
            'current_avg': y[-1],
            'period_avg': y.mean(),
            'max_usage': y.max(),
            'min_usage': y.min()
        }
    
    def generate_capacity_report(self, days: int = 7) -> str:
        """生成容量分析报告"""
        report_lines = []
        report_lines.append("=" * 60)
        report_lines.append(f"容量分析报告 - {date.today()}")
        report_lines.append("=" * 60)
        
        # 获取当日汇总
        today_summary = self.get_daily_summary()
        if today_summary:
            report_lines.append("\n📊 当日容量汇总:")
            for item in today_summary:
                resource_type = item['resource_type']
                total = item['total_count']
                normal = item['normal_count']
                warning = item['warning_count']
                critical = item['critical_count']
                avg_usage = item['avg_usage_rate']
                
                report_lines.append(f"  {resource_type.upper()}:")
                report_lines.append(f"    总数: {total}, 正常: {normal}, 观察: {warning}, 警告: {critical}")
                report_lines.append(f"    平均使用率: {avg_usage:.2f}%")
        
        # 分析各资源类型趋势
        resource_types = ['storage', 'database', 'container', 'virtualization']
        report_lines.append(f"\n📈 {days}日使用率趋势分析:")
        
        for resource_type in resource_types:
            try:
                trend_analysis = self.analyze_usage_trend(resource_type, days)
                report_lines.append(f"  {resource_type.upper()}: {trend_analysis['message']}")
            except Exception as e:
                report_lines.append(f"  {resource_type.upper()}: 分析失败 - {e}")
        
        report_lines.append("\n" + "=" * 60)
        
        return "\n".join(report_lines)
    
    def export_data_to_excel(self, filename: str, days: int = 30):
        """导出数据到Excel文件"""
        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 导出各类型历史数据
                storage_df = self.query_storage_history(days)
                if not storage_df.empty:
                    storage_df.to_excel(writer, sheet_name='存储容量', index=False)
                
                database_df = self.query_database_history(days)
                if not database_df.empty:
                    database_df.to_excel(writer, sheet_name='数据库容量', index=False)
                
                container_df = self.query_container_history(days)
                if not container_df.empty:
                    container_df.to_excel(writer, sheet_name='容器容量', index=False)
                
                vm_df = self.query_virtualization_history(days)
                if not vm_df.empty:
                    vm_df.to_excel(writer, sheet_name='虚拟化容量', index=False)
            
            logger.info(f"数据已导出到: {filename}")
            
        except Exception as e:
            logger.error(f"导出Excel失败: {e}")

def main():
    """主函数"""
    analyzer = CapacityAnalyzer()
    
    try:
        if not analyzer.connect_db():
            return
        
        # 生成容量报告
        report = analyzer.generate_capacity_report(days=7)
        print(report)
        
        # 导出数据到Excel
        filename = f"capacity_data_{date.today().strftime('%Y%m%d')}.xlsx"
        analyzer.export_data_to_excel(filename, days=30)
        
    except Exception as e:
        logger.error(f"分析过程中出错: {e}")
    finally:
        analyzer.disconnect_db()

if __name__ == '__main__':
    main()
