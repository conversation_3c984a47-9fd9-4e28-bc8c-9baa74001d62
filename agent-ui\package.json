{"name": "agent-ui", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --check \"**/*.{ts,tsx,mdx}\" --cache", "format:fix": "prettier --write \"**/*.{ts,tsx,mdx}\" --cache", "typecheck": "tsc --noEmit", "validate": "pnpm run lint && pnpm run format && pnpm run typecheck"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "framer-motion": "^12.4.1", "lucide-react": "^0.474.0", "next": "15.2.3", "next-themes": "^0.4.4", "nuqs": "^2.3.2", "prettier-plugin-tailwindcss": "^0.6.11", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^9.0.3", "rehype-raw": "^7.0.0", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.0", "sonner": "^1.7.4", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "use-stick-to-bottom": "^1.0.46", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.3", "postcss": "^8", "prettier": "3.4.2", "tailwindcss": "^3.4.1", "typescript": "^5"}}