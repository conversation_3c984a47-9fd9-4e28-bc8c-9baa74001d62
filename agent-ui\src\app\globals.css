@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --color-border-default: 255, 255, 255, 0.2;
    --scrollbar-width: 0.1rem;
  }

  body {
    @apply bg-background/80 text-secondary;
  }
}

::-webkit-scrollbar {
  width: var(--scrollbar-width);
}

::-webkit-scrollbar-thumb {
  --tw-border-opacity: 1;
  @apply bg-border;
  border-color: rgba(255, 255, 255, var(--tw-border-opacity));
  border-radius: 9999px;
  border-width: 1px;
}

::-webkit-scrollbar-track {
  background-color: transparent;
  border-radius: 9999px;
}
