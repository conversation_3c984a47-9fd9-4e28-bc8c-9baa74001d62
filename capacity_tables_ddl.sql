-- ========================================
-- 容量监控数据库表结构设计
-- 创建日期: 2024-12-01
-- 描述: 用于记录每日容量监控数据的四个核心表
-- ========================================

-- 1. 存储容量历史表
CREATE TABLE IF NOT EXISTS storage_capacity_history (
    id SERIAL PRIMARY KEY,
    record_date DATE NOT NULL,
    pool_name VARCHAR(100) NOT NULL,
    pool_id VARCHAR(50) NOT NULL,
    total_capacity_gb DECIMAL(15,2) NOT NULL DEFAULT 0,
    used_capacity_gb DECIMAL(15,2) NOT NULL DEFAULT 0,
    available_capacity_gb DECIMAL(15,2) NOT NULL DEFAULT 0,
    usage_rate DECIMAL(5,2) NOT NULL DEFAULT 0,
    health_status VARCHAR(20) NOT NULL DEFAULT '正常',
    has_anomaly BOOLEAN NOT NULL DEFAULT FALSE,
    measures TEXT,
    data_source VARCHAR(50) DEFAULT 'API',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引和约束
    UNIQUE(record_date, pool_id),
    INDEX idx_storage_date (record_date),
    INDEX idx_storage_pool (pool_id),
    INDEX idx_storage_health (health_status),
    CHECK (usage_rate >= 0 AND usage_rate <= 100),
    CHECK (total_capacity_gb >= 0),
    CHECK (used_capacity_gb >= 0),
    CHECK (available_capacity_gb >= 0)
);

-- 2. 数据库容量历史表
CREATE TABLE IF NOT EXISTS database_capacity_history (
    id SERIAL PRIMARY KEY,
    record_date DATE NOT NULL,
    db_name VARCHAR(100) NOT NULL,
    db_id VARCHAR(50) NOT NULL,
    total_capacity_gb DECIMAL(15,2) NOT NULL DEFAULT 0,
    used_capacity_gb DECIMAL(15,2) NOT NULL DEFAULT 0,
    available_capacity_gb DECIMAL(15,2) NOT NULL DEFAULT 0,
    usage_rate DECIMAL(5,2) NOT NULL DEFAULT 0,
    health_status VARCHAR(20) NOT NULL DEFAULT '正常',
    has_anomaly BOOLEAN NOT NULL DEFAULT FALSE,
    measures TEXT,
    data_source VARCHAR(50) DEFAULT 'API',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引和约束
    UNIQUE(record_date, db_id),
    INDEX idx_database_date (record_date),
    INDEX idx_database_db (db_id),
    INDEX idx_database_health (health_status),
    CHECK (usage_rate >= 0 AND usage_rate <= 100),
    CHECK (total_capacity_gb >= 0),
    CHECK (used_capacity_gb >= 0),
    CHECK (available_capacity_gb >= 0)
);

-- 3. 容器集群容量历史表
CREATE TABLE IF NOT EXISTS container_capacity_history (
    id SERIAL PRIMARY KEY,
    record_date DATE NOT NULL,
    cluster_name VARCHAR(100) NOT NULL,
    cluster_id VARCHAR(50) NOT NULL,
    node_count INTEGER NOT NULL DEFAULT 0,
    pod_count INTEGER NOT NULL DEFAULT 0,
    
    -- CPU资源
    cpu_total_cores DECIMAL(10,2) NOT NULL DEFAULT 0,
    cpu_used_cores DECIMAL(10,2) NOT NULL DEFAULT 0,
    cpu_available_cores DECIMAL(10,2) NOT NULL DEFAULT 0,
    cpu_usage_rate DECIMAL(5,2) NOT NULL DEFAULT 0,
    
    -- 内存资源
    memory_total_gb DECIMAL(15,2) NOT NULL DEFAULT 0,
    memory_used_gb DECIMAL(15,2) NOT NULL DEFAULT 0,
    memory_available_gb DECIMAL(15,2) NOT NULL DEFAULT 0,
    memory_usage_rate DECIMAL(5,2) NOT NULL DEFAULT 0,
    
    -- 存储资源
    storage_total_gb DECIMAL(15,2) NOT NULL DEFAULT 0,
    storage_used_gb DECIMAL(15,2) NOT NULL DEFAULT 0,
    storage_available_gb DECIMAL(15,2) NOT NULL DEFAULT 0,
    storage_usage_rate DECIMAL(5,2) NOT NULL DEFAULT 0,
    
    -- 健康状态
    health_status VARCHAR(20) NOT NULL DEFAULT '正常',
    has_anomaly BOOLEAN NOT NULL DEFAULT FALSE,
    measures TEXT,
    data_source VARCHAR(50) DEFAULT 'API',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引和约束
    UNIQUE(record_date, cluster_id),
    INDEX idx_container_date (record_date),
    INDEX idx_container_cluster (cluster_id),
    INDEX idx_container_health (health_status),
    CHECK (cpu_usage_rate >= 0 AND cpu_usage_rate <= 100),
    CHECK (memory_usage_rate >= 0 AND memory_usage_rate <= 100),
    CHECK (storage_usage_rate >= 0 AND storage_usage_rate <= 100),
    CHECK (node_count >= 0),
    CHECK (pod_count >= 0)
);

-- 4. 虚拟化集群容量历史表
CREATE TABLE IF NOT EXISTS virtualization_capacity_history (
    id SERIAL PRIMARY KEY,
    record_date DATE NOT NULL,
    cluster_name VARCHAR(100) NOT NULL,
    cluster_id VARCHAR(50) NOT NULL,
    host_number INTEGER NOT NULL DEFAULT 0,
    vm_number INTEGER NOT NULL DEFAULT 0,
    
    -- CPU资源
    cpu_total_cores DECIMAL(15,2) NOT NULL DEFAULT 0,
    cpu_used_cores DECIMAL(15,2) NOT NULL DEFAULT 0,
    cpu_available_cores DECIMAL(15,2) NOT NULL DEFAULT 0,
    cpu_usage_rate DECIMAL(5,2) NOT NULL DEFAULT 0,
    
    -- 内存资源
    memory_total_gb DECIMAL(15,2) NOT NULL DEFAULT 0,
    memory_used_gb DECIMAL(15,2) NOT NULL DEFAULT 0,
    memory_available_gb DECIMAL(15,2) NOT NULL DEFAULT 0,
    memory_usage_rate DECIMAL(5,2) NOT NULL DEFAULT 0,
    
    -- 健康状态
    health_status VARCHAR(20) NOT NULL DEFAULT '正常',
    has_anomaly BOOLEAN NOT NULL DEFAULT FALSE,
    measures TEXT,
    data_source VARCHAR(50) DEFAULT 'API',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引和约束
    UNIQUE(record_date, cluster_id),
    INDEX idx_vm_date (record_date),
    INDEX idx_vm_cluster (cluster_id),
    INDEX idx_vm_health (health_status),
    CHECK (cpu_usage_rate >= 0 AND cpu_usage_rate <= 100),
    CHECK (memory_usage_rate >= 0 AND memory_usage_rate <= 100),
    CHECK (host_number >= 0),
    CHECK (vm_number >= 0)
);

-- 创建数据收集日志表
CREATE TABLE IF NOT EXISTS capacity_collection_log (
    id SERIAL PRIMARY KEY,
    collection_date DATE NOT NULL,
    collection_type VARCHAR(20) NOT NULL, -- storage, database, container, virtualization
    status VARCHAR(20) NOT NULL DEFAULT 'SUCCESS', -- SUCCESS, FAILED, PARTIAL
    records_count INTEGER NOT NULL DEFAULT 0,
    error_message TEXT,
    execution_time_seconds DECIMAL(8,3),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_log_date (collection_date),
    INDEX idx_log_type (collection_type),
    INDEX idx_log_status (status)
);

-- 添加表注释
COMMENT ON TABLE storage_capacity_history IS '存储容量历史数据表';
COMMENT ON TABLE database_capacity_history IS '数据库容量历史数据表';
COMMENT ON TABLE container_capacity_history IS '容器集群容量历史数据表';
COMMENT ON TABLE virtualization_capacity_history IS '虚拟化集群容量历史数据表';
COMMENT ON TABLE capacity_collection_log IS '容量数据收集日志表';

-- 创建视图：当日容量汇总
CREATE OR REPLACE VIEW daily_capacity_summary AS
SELECT 
    record_date,
    'storage' as resource_type,
    COUNT(*) as total_count,
    SUM(CASE WHEN health_status = '正常' THEN 1 ELSE 0 END) as normal_count,
    SUM(CASE WHEN health_status = '观察' THEN 1 ELSE 0 END) as warning_count,
    SUM(CASE WHEN health_status = '警告' THEN 1 ELSE 0 END) as critical_count,
    AVG(usage_rate) as avg_usage_rate
FROM storage_capacity_history 
GROUP BY record_date

UNION ALL

SELECT 
    record_date,
    'database' as resource_type,
    COUNT(*) as total_count,
    SUM(CASE WHEN health_status = '正常' THEN 1 ELSE 0 END) as normal_count,
    SUM(CASE WHEN health_status = '观察' THEN 1 ELSE 0 END) as warning_count,
    SUM(CASE WHEN health_status = '警告' THEN 1 ELSE 0 END) as critical_count,
    AVG(usage_rate) as avg_usage_rate
FROM database_capacity_history 
GROUP BY record_date

UNION ALL

SELECT 
    record_date,
    'container' as resource_type,
    COUNT(*) as total_count,
    SUM(CASE WHEN health_status = '正常' THEN 1 ELSE 0 END) as normal_count,
    SUM(CASE WHEN health_status = '观察' THEN 1 ELSE 0 END) as warning_count,
    SUM(CASE WHEN health_status = '警告' THEN 1 ELSE 0 END) as critical_count,
    AVG((cpu_usage_rate + memory_usage_rate + storage_usage_rate) / 3) as avg_usage_rate
FROM container_capacity_history 
GROUP BY record_date

UNION ALL

SELECT 
    record_date,
    'virtualization' as resource_type,
    COUNT(*) as total_count,
    SUM(CASE WHEN health_status = '正常' THEN 1 ELSE 0 END) as normal_count,
    SUM(CASE WHEN health_status = '观察' THEN 1 ELSE 0 END) as warning_count,
    SUM(CASE WHEN health_status = '警告' THEN 1 ELSE 0 END) as critical_count,
    AVG((cpu_usage_rate + memory_usage_rate) / 2) as avg_usage_rate
FROM virtualization_capacity_history 
GROUP BY record_date;

COMMENT ON VIEW daily_capacity_summary IS '每日容量汇总视图';
