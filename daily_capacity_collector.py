#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
每日容量数据收集脚本
功能：调用容量监控API接口，将数据存储到历史表中
作者：容量监控系统
创建日期：2024-12-01
"""

import os
import sys
import time
import json
import logging
import requests
import psycopg2
from datetime import datetime, date
from typing import Dict, List, Optional, Any
from psycopg2.extras import RealDictCursor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('capacity_collector.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# API配置
API_BASE_URL = "http://127.0.0.1:5002"  # app.py的服务地址
API_TIMEOUT = 30

# 数据库配置
DB_CONFIG = {
    'host': '*************',
    'port': 5444,
    'database': 'cloud',
    'user': 'cloud',
    'password': 'Cl123456'
}

class CapacityCollector:
    """容量数据收集器"""
    
    def __init__(self):
        self.db_conn = None
        self.collection_date = date.today()
        
    def connect_db(self) -> bool:
        """连接数据库"""
        try:
            self.db_conn = psycopg2.connect(**DB_CONFIG)
            self.db_conn.autocommit = False
            logger.info("数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False
    
    def disconnect_db(self):
        """断开数据库连接"""
        if self.db_conn:
            self.db_conn.close()
            logger.info("数据库连接已关闭")
    
    def call_api(self, endpoint: str) -> Optional[Dict]:
        """调用API接口"""
        try:
            url = f"{API_BASE_URL}{endpoint}"
            logger.info(f"调用API: {url}")
            
            response = requests.get(url, timeout=API_TIMEOUT)
            response.raise_for_status()
            
            data = response.json()
            logger.info(f"API调用成功，状态: {data.get('status', 'unknown')}")
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"API调用失败 {url}: {e}")
            return None
        except json.JSONDecodeError as e:
            logger.error(f"API响应解析失败: {e}")
            return None
    
    def log_collection_result(self, collection_type: str, status: str, 
                            records_count: int, error_message: str = None, 
                            execution_time: float = 0):
        """记录收集结果到日志表"""
        try:
            cursor = self.db_conn.cursor()
            cursor.execute("""
                INSERT INTO capacity_collection_log 
                (collection_date, collection_type, status, records_count, error_message, execution_time_seconds)
                VALUES (%s, %s, %s, %s, %s, %s)
            """, (self.collection_date, collection_type, status, records_count, error_message, execution_time))
            
        except Exception as e:
            logger.error(f"记录收集日志失败: {e}")
    
    def collect_storage_data(self) -> bool:
        """收集存储容量数据"""
        start_time = time.time()
        collection_type = 'storage'
        
        try:
            logger.info("开始收集存储容量数据...")
            
            # 调用API获取数据
            api_data = self.call_api('/api/storage')
            if not api_data or api_data.get('status') != 'success':
                raise Exception("API返回数据异常")
            
            storage_pools = api_data.get('data', {}).get('storage_pools', [])
            if not storage_pools:
                logger.warning("未获取到存储池数据")
                return False
            
            # 插入数据库
            cursor = self.db_conn.cursor()
            
            # 先删除当日数据（如果存在）
            cursor.execute("DELETE FROM storage_capacity_history WHERE record_date = %s", 
                         (self.collection_date,))
            
            # 插入新数据
            insert_count = 0
            for pool in storage_pools:
                cursor.execute("""
                    INSERT INTO storage_capacity_history 
                    (record_date, pool_name, pool_id, total_capacity_gb, used_capacity_gb, 
                     available_capacity_gb, usage_rate, health_status, has_anomaly, measures)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    self.collection_date,
                    pool.get('pool_name', ''),
                    pool.get('pool_id', ''),
                    pool.get('total_capacity_gb', 0),
                    pool.get('used_capacity_gb', 0),
                    pool.get('available_capacity_gb', 0),
                    pool.get('usage_rate', 0),
                    pool.get('health_status', '正常'),
                    pool.get('has_anomaly', False),
                    pool.get('measures', '')
                ))
                insert_count += 1
            
            self.db_conn.commit()
            execution_time = time.time() - start_time
            
            logger.info(f"存储容量数据收集完成，插入 {insert_count} 条记录")
            self.log_collection_result(collection_type, 'SUCCESS', insert_count, 
                                     execution_time=execution_time)
            return True
            
        except Exception as e:
            self.db_conn.rollback()
            execution_time = time.time() - start_time
            error_msg = str(e)
            logger.error(f"存储容量数据收集失败: {error_msg}")
            self.log_collection_result(collection_type, 'FAILED', 0, error_msg, execution_time)
            return False
    
    def collect_database_data(self) -> bool:
        """收集数据库容量数据"""
        start_time = time.time()
        collection_type = 'database'
        
        try:
            logger.info("开始收集数据库容量数据...")
            
            # 调用API获取数据
            api_data = self.call_api('/api/database')
            if not api_data or api_data.get('status') != 'success':
                raise Exception("API返回数据异常")
            
            database_instances = api_data.get('data', {}).get('database_instances', [])
            if not database_instances:
                logger.warning("未获取到数据库实例数据")
                return False
            
            # 插入数据库
            cursor = self.db_conn.cursor()
            
            # 先删除当日数据（如果存在）
            cursor.execute("DELETE FROM database_capacity_history WHERE record_date = %s", 
                         (self.collection_date,))
            
            # 插入新数据
            insert_count = 0
            for db in database_instances:
                cursor.execute("""
                    INSERT INTO database_capacity_history 
                    (record_date, db_name, db_id, total_capacity_gb, used_capacity_gb, 
                     available_capacity_gb, usage_rate, health_status, has_anomaly, measures)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    self.collection_date,
                    db.get('db_name', ''),
                    db.get('db_id', ''),
                    db.get('total_capacity_gb', 0),
                    db.get('used_capacity_gb', 0),
                    db.get('available_capacity_gb', 0),
                    db.get('usage_rate', 0),
                    db.get('health_status', '正常'),
                    db.get('has_anomaly', False),
                    db.get('measures', '')
                ))
                insert_count += 1
            
            self.db_conn.commit()
            execution_time = time.time() - start_time
            
            logger.info(f"数据库容量数据收集完成，插入 {insert_count} 条记录")
            self.log_collection_result(collection_type, 'SUCCESS', insert_count, 
                                     execution_time=execution_time)
            return True
            
        except Exception as e:
            self.db_conn.rollback()
            execution_time = time.time() - start_time
            error_msg = str(e)
            logger.error(f"数据库容量数据收集失败: {error_msg}")
            self.log_collection_result(collection_type, 'FAILED', 0, error_msg, execution_time)
            return False

    def collect_container_data(self) -> bool:
        """收集容器集群容量数据"""
        start_time = time.time()
        collection_type = 'container'

        try:
            logger.info("开始收集容器集群容量数据...")

            # 调用API获取数据
            api_data = self.call_api('/api/container')
            if not api_data or api_data.get('status') != 'success':
                raise Exception("API返回数据异常")

            container_clusters = api_data.get('data', {}).get('container_clusters', [])
            if not container_clusters:
                logger.warning("未获取到容器集群数据")
                return False

            # 插入数据库
            cursor = self.db_conn.cursor()

            # 先删除当日数据（如果存在）
            cursor.execute("DELETE FROM container_capacity_history WHERE record_date = %s",
                         (self.collection_date,))

            # 插入新数据
            insert_count = 0
            for cluster in container_clusters:
                cursor.execute("""
                    INSERT INTO container_capacity_history
                    (record_date, cluster_name, cluster_id, node_count, pod_count,
                     cpu_total_cores, cpu_used_cores, cpu_available_cores, cpu_usage_rate,
                     memory_total_gb, memory_used_gb, memory_available_gb, memory_usage_rate,
                     storage_total_gb, storage_used_gb, storage_available_gb, storage_usage_rate,
                     health_status, has_anomaly, measures)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    self.collection_date,
                    cluster.get('cluster_name', ''),
                    cluster.get('cluster_id', ''),
                    cluster.get('node_count', 0),
                    cluster.get('pod_count', 0),
                    cluster.get('cpu_total_cores', 0),
                    cluster.get('cpu_used_cores', 0),
                    cluster.get('cpu_available_cores', 0),
                    cluster.get('cpu_usage_rate', 0),
                    cluster.get('memory_total_gb', 0),
                    cluster.get('memory_used_gb', 0),
                    cluster.get('memory_available_gb', 0),
                    cluster.get('memory_usage_rate', 0),
                    cluster.get('storage_total_gb', 0),
                    cluster.get('storage_used_gb', 0),
                    cluster.get('storage_available_gb', 0),
                    cluster.get('storage_usage_rate', 0),
                    cluster.get('health_status', '正常'),
                    cluster.get('has_anomaly', False),
                    cluster.get('measures', '')
                ))
                insert_count += 1

            self.db_conn.commit()
            execution_time = time.time() - start_time

            logger.info(f"容器集群容量数据收集完成，插入 {insert_count} 条记录")
            self.log_collection_result(collection_type, 'SUCCESS', insert_count,
                                     execution_time=execution_time)
            return True

        except Exception as e:
            self.db_conn.rollback()
            execution_time = time.time() - start_time
            error_msg = str(e)
            logger.error(f"容器集群容量数据收集失败: {error_msg}")
            self.log_collection_result(collection_type, 'FAILED', 0, error_msg, execution_time)
            return False

    def collect_virtualization_data(self) -> bool:
        """收集虚拟化集群容量数据"""
        start_time = time.time()
        collection_type = 'virtualization'

        try:
            logger.info("开始收集虚拟化集群容量数据...")

            # 调用API获取数据
            api_data = self.call_api('/api/virtualization')
            if not api_data or api_data.get('status') != 'success':
                raise Exception("API返回数据异常")

            vm_clusters = api_data.get('data', {}).get('vm_clusters', [])
            if not vm_clusters:
                logger.warning("未获取到虚拟化集群数据")
                return False

            # 插入数据库
            cursor = self.db_conn.cursor()

            # 先删除当日数据（如果存在）
            cursor.execute("DELETE FROM virtualization_capacity_history WHERE record_date = %s",
                         (self.collection_date,))

            # 插入新数据
            insert_count = 0
            for cluster in vm_clusters:
                cursor.execute("""
                    INSERT INTO virtualization_capacity_history
                    (record_date, cluster_name, cluster_id, host_number, vm_number,
                     cpu_total_cores, cpu_used_cores, cpu_available_cores, cpu_usage_rate,
                     memory_total_gb, memory_used_gb, memory_available_gb, memory_usage_rate,
                     health_status, has_anomaly, measures)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    self.collection_date,
                    cluster.get('cluster_name', ''),
                    cluster.get('cluster_id', ''),
                    cluster.get('host_number', 0),
                    cluster.get('vm_number', 0),
                    cluster.get('cpu_total_cores', 0),
                    cluster.get('cpu_used_cores', 0),
                    cluster.get('cpu_available_cores', 0),
                    cluster.get('cpu_usage_rate', 0),
                    cluster.get('memory_total_gb', 0),
                    cluster.get('memory_used_gb', 0),
                    cluster.get('memory_available_gb', 0),
                    cluster.get('memory_usage_rate', 0),
                    cluster.get('health_status', '正常'),
                    cluster.get('has_anomaly', False),
                    cluster.get('measures', '')
                ))
                insert_count += 1

            self.db_conn.commit()
            execution_time = time.time() - start_time

            logger.info(f"虚拟化集群容量数据收集完成，插入 {insert_count} 条记录")
            self.log_collection_result(collection_type, 'SUCCESS', insert_count,
                                     execution_time=execution_time)
            return True

        except Exception as e:
            self.db_conn.rollback()
            execution_time = time.time() - start_time
            error_msg = str(e)
            logger.error(f"虚拟化集群容量数据收集失败: {error_msg}")
            self.log_collection_result(collection_type, 'FAILED', 0, error_msg, execution_time)
            return False

    def collect_all_data(self) -> Dict[str, bool]:
        """收集所有类型的容量数据"""
        logger.info(f"开始执行每日容量数据收集任务 - {self.collection_date}")

        results = {}

        # 收集存储数据
        results['storage'] = self.collect_storage_data()
        time.sleep(2)  # 避免API调用过于频繁

        # 收集数据库数据
        results['database'] = self.collect_database_data()
        time.sleep(2)

        # 收集容器数据
        results['container'] = self.collect_container_data()
        time.sleep(2)

        # 收集虚拟化数据
        results['virtualization'] = self.collect_virtualization_data()

        # 统计结果
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)

        logger.info(f"容量数据收集任务完成: {success_count}/{total_count} 成功")

        if success_count == total_count:
            logger.info("✅ 所有数据收集成功")
        elif success_count > 0:
            logger.warning(f"⚠️ 部分数据收集成功: {results}")
        else:
            logger.error("❌ 所有数据收集失败")

        return results

    def run(self) -> bool:
        """运行数据收集任务"""
        try:
            # 连接数据库
            if not self.connect_db():
                return False

            # 收集所有数据
            results = self.collect_all_data()

            # 检查是否有成功的收集
            success_count = sum(1 for success in results.values() if success)
            return success_count > 0

        except Exception as e:
            logger.error(f"数据收集任务执行失败: {e}")
            return False
        finally:
            self.disconnect_db()

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("容量数据收集脚本启动")
    logger.info("=" * 60)

    try:
        collector = CapacityCollector()
        success = collector.run()

        if success:
            logger.info("容量数据收集任务执行成功")
            sys.exit(0)
        else:
            logger.error("容量数据收集任务执行失败")
            sys.exit(1)

    except KeyboardInterrupt:
        logger.info("用户中断执行")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行异常: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
