#!/usr/bin/env python3
"""
Word导出工具模块
用于将Markdown格式的容量报告转换为Word文档
"""

import os
import re
from datetime import datetime
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT
from docx.oxml.shared import OxmlElement, qn

def export_to_word(markdown_content, report_date, system_name, save_path):
    """将Markdown内容导出为Word文档格式"""

    # 确保保存路径存在
    if not save_path or save_path.strip() == "":
        save_path = "D:/work/LLM/reports/"

    os.makedirs(save_path, exist_ok=True)

    # 生成文件名
    safe_name = re.sub(r'[<>:"/\\|?*]', '_', system_name)
    filename = f"{safe_name}_{report_date.replace('-', '')}.docx"
    filepath = os.path.join(save_path, filename)

    # 创建Word文档
    doc = Document()

    # 设置文档样式
    setup_document_styles(doc)

    # 添加文档标题
    title = doc.add_heading(system_name, 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER

    # 添加报告信息
    info_para = doc.add_paragraph()
    info_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    info_para.add_run(f"报告日期：{report_date}").bold = True
    info_para.add_run("\n")
    info_para.add_run(f"生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}").bold = True

    # 添加空行
    doc.add_paragraph()

    # 解析Markdown内容并转换为Word
    lines = markdown_content.split('\n')
    current_table = None
    table_rows = []

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # 处理标题
        if line.startswith('# '):
            continue  # 跳过主标题，已在开头处理
        elif line.startswith('## '):
            # 结束当前表格（如果有）
            if current_table is not None:
                create_word_table(doc, table_rows)
                current_table = None
                table_rows = []

            # 添加二级标题
            heading = doc.add_heading(line[3:], level=1)
            heading.alignment = WD_ALIGN_PARAGRAPH.LEFT

        elif line.startswith('### '):
            # 结束当前表格（如果有）
            if current_table is not None:
                create_word_table(doc, table_rows)
                current_table = None
                table_rows = []

            # 添加三级标题
            doc.add_heading(line[4:], level=2)

        # 处理表格
        elif line.startswith('|') and '|' in line:
            cells = [cell.strip() for cell in line.split('|')[1:-1]]

            # 检查是否是分隔行
            if all(cell.startswith('-') for cell in cells):
                continue

            # 添加表格行
            table_rows.append(cells)
            if current_table is None:
                current_table = True

        else:
            # 结束当前表格（如果有）
            if current_table is not None:
                create_word_table(doc, table_rows)
                current_table = None
                table_rows = []

            # 处理其他内容
            if line.startswith('**') and line.endswith('**'):
                # 加粗文本
                para = doc.add_paragraph()
                para.add_run(line[2:-2]).bold = True
            elif line.startswith('- '):
                # 列表项
                para = doc.add_paragraph(line[2:], style='List Bullet')
            elif line.startswith('数据来源：'):
                # 数据来源
                para = doc.add_paragraph()
                para.add_run(line).bold = True
            else:
                if line and not line.startswith('---'):
                    # 普通段落
                    doc.add_paragraph(line)

    # 处理最后的表格
    if current_table is not None:
        create_word_table(doc, table_rows)

    # 保存Word文件
    doc.save(filepath)

    return filepath

def setup_document_styles(doc):
    """设置文档样式"""
    # 设置默认字体
    style = doc.styles['Normal']
    font = style.font
    font.name = 'Microsoft YaHei'
    font.size = Pt(12)

def create_word_table(doc, table_rows):
    """创建Word表格"""
    if not table_rows:
        return

    # 创建表格
    table = doc.add_table(rows=len(table_rows), cols=len(table_rows[0]))
    table.alignment = WD_TABLE_ALIGNMENT.CENTER
    table.style = 'Table Grid'

    # 填充表格数据
    for row_idx, row_data in enumerate(table_rows):
        for col_idx, cell_data in enumerate(row_data):
            cell = table.cell(row_idx, col_idx)
            cell.text = cell_data

            # 设置表头样式
            if row_idx == 0:
                for paragraph in cell.paragraphs:
                    for run in paragraph.runs:
                        run.bold = True
                # 设置表头背景色
                set_cell_background_color(cell, 'F0F0F0')

            # 设置单元格对齐
            for paragraph in cell.paragraphs:
                paragraph.alignment = WD_ALIGN_PARAGRAPH.CENTER

    # 添加表格后的空行
    doc.add_paragraph()

def set_cell_background_color(cell, color):
    """设置单元格背景色"""
    try:
        from docx.oxml import parse_xml
        shading_elm = parse_xml(f'<w:shd {{{cell._tc.nsmap["w"]}}} w:fill="{color}"/>')
        cell._tc.get_or_add_tcPr().append(shading_elm)
    except:
        # 如果设置背景色失败，忽略错误
        pass

# 为了兼容性，保留原函数名
def export_to_html(markdown_content, report_date, system_name, save_path):
    """兼容性函数，实际调用Word导出"""
    return export_to_word(markdown_content, report_date, system_name, save_path)
