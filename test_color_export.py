#!/usr/bin/env python3
"""
测试颜色导出功能
"""

from html_export import export_to_word

# 测试用的Markdown内容，包含不同健康度的数据
test_markdown = """
# 容量检查报告

## 存储容量报告

### 存储池容量详情

| 存储池名称 | 总容量(GB) | 已用容量(GB) | 可用容量(GB) | 使用率 | 健康状态 |
|-----------|-----------|-------------|-------------|--------|----------|
| 后沙峪中端数据库存储池 | 1000 | 850 | 150 | 85.0% | 正常 |
| 后沙峪中端虚拟化存储池 | 2000 | 1850 | 150 | 92.5% | 观察 |
| 嘉兴中端数据库存储池 | 1500 | 1440 | 60 | 96.0% | 警告 |

## 数据库容量报告

### 数据库实例容量详情

| 数据库名称 | 总容量(GB) | 已用容量(GB) | 可用容量(GB) | 使用率 | 健康状态 |
|-----------|-----------|-------------|-------------|--------|----------|
| 生产数据库1 | 500 | 400 | 100 | 80.0% | 正常 |
| 生产数据库2 | 800 | 720 | 80 | 90.0% | 观察 |
| 生产数据库3 | 600 | 588 | 12 | 98.0% | 警告 |

## 容器容量报告

### 容器集群容量详情

| 集群名称 | CPU使用率 | 内存使用率 | 存储使用率 | 健康状态 |
|---------|----------|-----------|-----------|----------|
| 生产集群1 | 75.0% | 70.0% | 85.0% | 正常 |
| 生产集群2 | 85.0% | 88.0% | 92.0% | 观察 |
| 生产集群3 | 95.0% | 92.0% | 97.0% | 警告 |

## 虚拟化容量报告

### 虚拟化集群容量详情

| 集群名称 | CPU使用率 | 内存使用率 | 存储使用率 | 健康状态 |
|---------|----------|-----------|-----------|----------|
| 虚拟化集群1 | 70.0% | 65.0% | 85.0% | 正常 |
| 虚拟化集群2 | 80.0% | 82.0% | 92.0% | 观察 |
| 虚拟化集群3 | 90.0% | 88.0% | 97.0% | 警告 |

**数据来源：** 容量监控系统
**生成时间：** 2024-12-01 10:00:00
"""

if __name__ == '__main__':
    # 测试导出功能
    try:
        file_path = export_to_word(
            test_markdown, 
            '2024-12-01', 
            '容量检查报告测试', 
            'D:/work/LLM/test_reports'
        )
        print(f"测试文档已生成: {file_path}")
        print("请打开文档检查颜色是否正确应用")
        print("\n颜色说明:")
        print("🟢 绿色 - 正常状态")
        print("🟡 黄色 - 观察状态") 
        print("🔴 红色 - 警告状态")
    except Exception as e:
        print(f"测试失败: {e}")
